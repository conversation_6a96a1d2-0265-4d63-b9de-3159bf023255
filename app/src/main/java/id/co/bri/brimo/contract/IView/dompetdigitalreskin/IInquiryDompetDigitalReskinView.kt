package id.co.bri.brimo.contract.IView.dompetdigitalreskin

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseTransactionView
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse

interface IInquiryDompetDigitalReskinView : IBaseTransactionView {

    fun onGetDataConfirmation(generalConfirmationResponse: GeneralConfirmationResponse)

    fun onExceptionTrxExpired(desc: String)
}